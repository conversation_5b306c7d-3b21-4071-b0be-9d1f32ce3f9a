"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { EquipmentTag } from "@/types/equipment-tag";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ClipboardCheck, 
  Plus, 
  Building, 
  MapPin, 
  Package, 
  Tag as TagIcon,
  Calendar,
  User
} from "lucide-react";

type EquipmentTagWithChecklistInfo = EquipmentTag & {
  hasChecklists?: boolean;
  checklistCount?: number;
  lastChecklistDate?: string;
  availableAttempts?: number[];
  latestAttempt?: number;
};

interface AttemptSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  equipmentTag: EquipmentTagWithChecklistInfo | null;
}

export function AttemptSelectionDialog({ isOpen, onClose, equipmentTag }: AttemptSelectionDialogProps) {
  const router = useRouter();
  const [selectedAttempt, setSelectedAttempt] = useState<number | null>(null);

  const handleCreateNewChecklist = () => {
    if (!equipmentTag) return;
    
    // Calculate next attempt number
    const nextAttempt = (equipmentTag.latestAttempt || 0) + 1;
    
    // Navigate to checklist page with equipment tag and new attempt
    router.push(`/checklist?equipment=${equipmentTag.id}&attempt=${nextAttempt}`);
    onClose();
  };

  const handleSelectExistingAttempt = () => {
    if (!equipmentTag || selectedAttempt === null) return;
    
    // Navigate to checklist page with equipment tag and selected attempt
    router.push(`/checklist?equipment=${equipmentTag.id}&attempt=${selectedAttempt}`);
    onClose();
  };

  const handleViewAllChecklists = () => {
    if (!equipmentTag) return;
    
    // Navigate to checklist page with equipment tag filter
    router.push(`/checklist?equipment=${equipmentTag.id}&view=all`);
    onClose();
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (!equipmentTag) return null;

  const availableAttempts = equipmentTag.availableAttempts || [];
  const nextAttempt = (equipmentTag.latestAttempt || 0) + 1;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl sm:max-w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <ClipboardCheck className="h-5 w-5" />
            <span>PPM Attempt Selection</span>
          </DialogTitle>
          <DialogDescription className="text-sm">
            Choose how you want to proceed with the checklist for this equipment.
          </DialogDescription>
        </DialogHeader>

        {/* Equipment Information */}
        <Card className="bg-muted/30">
          <CardContent className="p-3 sm:p-4">
            <div className="space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                <div className="flex items-center gap-2">
                  <TagIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{equipmentTag.tagNumber}</span>
                </div>
                <Badge variant="outline" className="text-xs w-fit">
                  {equipmentTag.clientName}
                </Badge>
              </div>

              <div className="grid grid-cols-1 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="break-words">{equipmentTag.equipmentName}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="break-words">{equipmentTag.building}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="break-words">{equipmentTag.location}</span>
                </div>
                {equipmentTag.lastChecklistDate && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span className="break-words">Last checklist: {formatDate(equipmentTag.lastChecklistDate)}</span>
                  </div>
                )}
              </div>

              {equipmentTag.hasChecklists && (
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 pt-2 border-t">
                  <ClipboardCheck className="h-4 w-4 text-green-600 flex-shrink-0" />
                  <span className="text-sm text-green-700">
                    {equipmentTag.checklistCount} existing checklist{(equipmentTag.checklistCount || 0) > 1 ? 's' : ''}
                    {availableAttempts.length > 0 && (
                      <span className="block sm:inline sm:ml-2">
                        (PPM attempts: {availableAttempts.join(', ')})
                      </span>
                    )}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          {/* Create New Checklist */}
          <Card className="border-2 border-green-200 bg-green-50/50">
            <CardContent className="p-3 sm:p-4">
              <div className="flex flex-col gap-3">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Plus className="h-5 w-5 text-green-600" />
                    <h3 className="font-medium text-green-900">Create New Checklist</h3>
                  </div>
                  <p className="text-sm text-green-700">
                    Start a new inspection for PPM attempt #{nextAttempt}
                  </p>
                  <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300 w-fit">
                    PPM Attempt {nextAttempt}
                  </Badge>
                </div>
                <Button
                  onClick={handleCreateNewChecklist}
                  className="bg-green-600 hover:bg-green-700 w-full sm:w-auto sm:self-end"
                  size="sm"
                >
                  <Plus className="h-4 w-4 mr-2 sm:hidden" />
                  Create New
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Continue Existing Attempts */}
          {availableAttempts.length > 0 && (
            <Card className="border-2 border-blue-200 bg-blue-50/50">
              <CardContent className="p-3 sm:p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <ClipboardCheck className="h-5 w-5 text-blue-600" />
                    <h3 className="font-medium text-blue-900">Continue Existing Attempt</h3>
                  </div>
                  <p className="text-sm text-blue-700">
                    Continue working on a previous PPM attempt
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {availableAttempts.map((attempt) => (
                      <Badge
                        key={attempt}
                        variant={selectedAttempt === attempt ? "default" : "outline"}
                        className={`cursor-pointer transition-colors text-xs px-2 py-1 ${
                          selectedAttempt === attempt
                            ? "bg-blue-600 text-white border-blue-600"
                            : "bg-blue-100 text-blue-800 border-blue-300 hover:bg-blue-200"
                        }`}
                        onClick={() => setSelectedAttempt(selectedAttempt === attempt ? null : attempt)}
                      >
                        PPM Attempt {attempt}
                      </Badge>
                    ))}
                  </div>

                  <Button
                    onClick={handleSelectExistingAttempt}
                    disabled={selectedAttempt === null}
                    variant="outline"
                    className="border-blue-300 text-blue-700 hover:bg-blue-100 w-full sm:w-auto"
                    size="sm"
                  >
                    <ClipboardCheck className="h-4 w-4 mr-2 sm:hidden" />
                    Continue Selected Attempt
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* View All Checklists */}
          {equipmentTag.hasChecklists && (
            <Card className="border-2 border-gray-200 bg-gray-50/50">
              <CardContent className="p-3 sm:p-4">
                <div className="flex flex-col gap-3">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <User className="h-5 w-5 text-gray-600" />
                      <h3 className="font-medium text-gray-900">View All Checklists</h3>
                    </div>
                    <p className="text-sm text-gray-700">
                      Review all completed and pending checklists for this equipment
                    </p>
                  </div>
                  <Button
                    onClick={handleViewAllChecklists}
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-100 w-full sm:w-auto sm:self-end"
                    size="sm"
                  >
                    <User className="h-4 w-4 mr-2 sm:hidden" />
                    View All
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 