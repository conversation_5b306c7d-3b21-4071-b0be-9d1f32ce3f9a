"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Download, 
  X,
  FileText,
  FileSpreadsheet,
  Calendar,
  User,
  Hash,
  Trash2
} from 'lucide-react';
import { ExportQueueItem } from '@/types/export-queue';
import { format } from 'date-fns';

interface QueueItemCardProps {
  item: ExportQueueItem;
  onCancel?: (queueId: string) => void;
  onDownload?: (downloadUrl: string, fileName: string) => void;
  onDelete?: (queueId: string) => void;
}

export function QueueItemCard({ item, onCancel, onDownload, onDelete }: QueueItemCardProps) {
  const getStatusIcon = () => {
    switch (item.status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-400" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-400" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-400" />;
    }
  };

  const getStatusColor = () => {
    switch (item.status) {
      case 'pending': return 'bg-gray-900 border-yellow-500 text-yellow-100';
      case 'processing': return 'bg-gray-900 border-blue-500 text-blue-100';
      case 'completed': return 'bg-gray-900 border-green-500 text-green-100';
      case 'failed': return 'bg-gray-900 border-red-500 text-red-100';
    }
  };

  const getStatusText = () => {
    switch (item.status) {
      case 'pending': return 'Queued';
      case 'processing': return 'Processing';
      case 'completed': return 'Ready';
      case 'failed': return 'Failed';
    }
  };

  const getTypeIcon = () => {
    return item.type === 'pdf' 
      ? <FileText className="h-3 w-3 text-gray-300" />
      : <FileSpreadsheet className="h-3 w-3 text-gray-300" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getProgressStageText = () => {
    if (!item.progress) return '';
    
    switch (item.progress.stage) {
      case 'queued': return 'Waiting in queue...';
      case 'generating': return `Processing ${item.progress.currentIndex} of ${item.progress.totalCount}`;
      case 'merging': return 'Combining documents...';
      case 'uploading': return 'Uploading to storage...';
      case 'complete': return 'Processing complete!';
      default: return `${item.progress.percentage}% complete`;
    }
  };

  const isExpired = () => {
    if (!item.result?.expiresAt) return false;
    return new Date(item.result.expiresAt.toDate()) < new Date();
  };

  const getExpirationText = () => {
    if (!item.result?.expiresAt) return '';
    const expiryDate = item.result.expiresAt.toDate();
    const now = new Date();
    const diffHours = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffHours < 0) return 'Expired';
    if (diffHours < 24) return `Expires in ${diffHours}h`;
    const diffDays = Math.ceil(diffHours / 24);
    return `Expires in ${diffDays}d`;
  };

  const getCompletionInfo = () => {
    // Handle completed exports
    if (item.status === 'completed' && item.completedAt) {
      const completedTime = item.completedAt.toDate();
      const startTime = item.startedAt?.toDate() || item.createdAt.toDate();
      const processingDuration = Math.round((completedTime.getTime() - startTime.getTime()) / 1000);
      
      const formatDuration = (seconds: number) => {
        if (seconds < 60) return `${seconds}s`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
      };
      
      return {
        completedAt: format(completedTime, 'MMM dd, HH:mm:ss'),
        duration: formatDuration(processingDuration),
        status: 'completed'
      };
    }
    
    // Handle failed exports
    if (item.status === 'failed' && item.completedAt) {
      const failedTime = item.completedAt.toDate();
      const startTime = item.startedAt?.toDate() || item.createdAt.toDate();
      const processingDuration = Math.round((failedTime.getTime() - startTime.getTime()) / 1000);
      
      const formatDuration = (seconds: number) => {
        if (seconds < 60) return `${seconds}s`;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
      };
      
      return {
        completedAt: format(failedTime, 'MMM dd, HH:mm:ss'),
        duration: formatDuration(processingDuration),
        status: 'failed'
      };
    }
    
    return null;
  };

  const handleDownload = () => {
    if (item.result?.downloadUrl && item.result?.fileName && onDownload) {
      onDownload(item.result.downloadUrl, item.result.fileName);
    }
  };

  const handleCancel = () => {
    if (onCancel && item.status === 'pending') {
      onCancel(item.id);
    }
  };

  const handleDelete = () => {
    if (onDelete && (item.status === 'completed' || item.status === 'failed')) {
      if (confirm('Are you sure you want to delete this export record?')) {
        onDelete(item.id);
      }
    }
  };

  return (
    <Card className={`transition-all duration-200 ${getStatusColor()} border-l-4`}>
      <CardContent className="p-3">
        {/* Compact Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            {getTypeIcon()}
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-sm text-gray-100">
                {item.type.toUpperCase()}
              </h3>
              <Badge variant="outline" className="text-xs px-1 py-0 h-4 border-gray-600 text-gray-300">
                {item.checklistCount}
              </Badge>
              <span className="text-xs text-gray-400">•</span>
              <span className="text-xs text-gray-400">{getStatusText()}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            {/* Progress for processing items */}
            {item.status === 'processing' && item.progress && (
              <div className="flex items-center gap-1">
                <Progress value={item.progress.percentage} className="w-16 h-1" />
                <span className="text-xs font-medium text-gray-300 min-w-[2rem]">{item.progress.percentage}%</span>
              </div>
            )}
            
            {/* Action buttons */}
            {item.status === 'completed' && item.result && !isExpired() && (
              <Button
                onClick={handleDownload}
                size="sm"
                className="h-6 px-2 text-xs bg-green-600 hover:bg-green-700 text-white"
              >
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            )}
            
            {item.status === 'pending' && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancel}
                className="h-6 px-2 text-xs border-gray-600 text-gray-300 hover:bg-gray-800"
              >
                <X className="h-3 w-3 mr-1" />
                Cancel
              </Button>
            )}

            {(item.status === 'completed' || item.status === 'failed') && onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                className="h-6 px-2 text-xs border-red-600 text-red-400 hover:bg-red-900/20"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Compact Metadata Row */}
        <div className="flex items-center gap-4 text-xs text-gray-400 mb-2">
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>{format(item.createdAt.toDate(), 'MMM dd, HH:mm')}</span>
          </div>
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span className="truncate max-w-[120px]">{item.metadata.requestedBy}</span>
          </div>
          {item.metadata.clientName && (
            <div className="flex items-center gap-1">
              <Hash className="h-3 w-3" />
              <span className="truncate max-w-[100px]">{item.metadata.clientName}</span>
            </div>
          )}
          {item.metadata.dateRange && (
            <div className="flex items-center gap-1">
              <span className="truncate max-w-[120px]">{item.metadata.dateRange}</span>
            </div>
          )}
          {(() => {
            const completionInfo = getCompletionInfo();
            return completionInfo && (
              <div className="flex items-center gap-1">
                {completionInfo.status === 'completed' ? (
                  <>
                    <CheckCircle className="h-3 w-3 text-green-400" />
                    <span className="text-green-300">Generated in {completionInfo.duration}</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-3 w-3 text-red-400" />
                    <span className="text-red-300">Failed after {completionInfo.duration}</span>
                  </>
                )}
              </div>
            );
          })()}
        </div>

        {/* Compact Progress Details */}
        {item.status === 'processing' && item.progress && (
          <div className="p-2 bg-gray-800/50 rounded border border-gray-700 mb-2">
            <p className="text-xs font-medium text-gray-200 mb-1">
              {getProgressStageText()}
            </p>
            {item.progress.currentChecklist && (
              <p className="text-xs text-gray-400 truncate">
                Current: {item.progress.currentChecklist}
              </p>
            )}
          </div>
        )}

        {/* Compact Success Details */}
        {item.status === 'completed' && item.result && (
          <div className="p-2 bg-gray-800/50 rounded border border-gray-700 mb-2">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-3">
                <span className="text-gray-400">Size: <span className="text-gray-200">{formatFileSize(item.result.fileSize)}</span></span>
                <span className="text-gray-400">Processed: <span className="text-gray-200">{item.result.processedCount}</span></span>
                {item.result.failedCount > 0 && (
                  <span className="text-gray-400">Failed: <span className="text-red-400">{item.result.failedCount}</span></span>
                )}
              </div>
              <span className="text-gray-400">{getExpirationText()}</span>
            </div>
            {isExpired() && (
              <div className="mt-1 p-1 bg-red-900/20 border border-red-600 rounded text-xs text-red-400">
                Expired - no longer available
              </div>
            )}
          </div>
        )}

        {/* Compact Error Details */}
        {item.status === 'failed' && item.error && (
          <div className="p-2 bg-gray-800/50 rounded border border-gray-700">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-3 w-3 text-red-400 flex-shrink-0 mt-0.5" />
              <div className="min-w-0 flex-1">
                <p className="text-xs font-medium text-red-400 mb-1">Export Failed</p>
                <p className="text-xs text-red-300 truncate">{item.error}</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 