import * as XLSX from 'xlsx';
import { ChecklistData } from '../types/checklist-types';

// Field configurations (copied from client-side config)
interface ChecklistFieldConfig {
  key: string;
  label: string;
  type: 'status' | 'number';
  unit?: string;
  section: 'mechanical' | 'electrical' | 'sequence';
}

const MECHANICAL_CHECKS: ChecklistFieldConfig[] = [
  {
    key: 'airFlowDirection',
    label: 'Air Flow Direction',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'bearingCondition',
    label: 'Bearing Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'beltCondition',
    label: 'Belt Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'beltTension',
    label: 'Belt Tension',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'couplingCondition',
    label: 'Coupling Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'fanBladeCondition',
    label: 'Fan Blade Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'filterCondition',
    label: 'Filter Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'housingCondition',
    label: 'Housing Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'lubricationOilLevel',
    label: 'Lubrication / Oil Level',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'mountingBoltsCondition',
    label: 'Mounting Bolts Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'noiseVibration',
    label: 'Noise / Vibration',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'pulleyCondition',
    label: 'Pulley Condition',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'rpm',
    label: 'RPM',
    type: 'number',
    unit: 'rpm',
    section: 'mechanical'
  },
  {
    key: 'shaftCondition',
    label: 'Shaft Condition',
    type: 'status',
    section: 'mechanical'
  }
];

const ELECTRICAL_CHECKS: ChecklistFieldConfig[] = [
  {
    key: 'amperage',
    label: 'Amperage',
    type: 'number',
    unit: 'A',
    section: 'electrical'
  },
  {
    key: 'amperageL1',
    label: 'Amperage L1',
    type: 'number',
    unit: 'A',
    section: 'electrical'
  },
  {
    key: 'amperageL2',
    label: 'Amperage L2',
    type: 'number',
    unit: 'A',
    section: 'electrical'
  },
  {
    key: 'amperageL3',
    label: 'Amperage L3',
    type: 'number',
    unit: 'A',
    section: 'electrical'
  },
  {
    key: 'contactorCondition',
    label: 'Contactor Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'controlWiringCondition',
    label: 'Control Wiring Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'earthingCondition',
    label: 'Earthing Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'fuseCondition',
    label: 'Fuse Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'insulationResistance',
    label: 'Insulation Resistance',
    type: 'number',
    unit: 'MΩ',
    section: 'electrical'
  },
  {
    key: 'mcbCondition',
    label: 'MCB Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'motorTerminalCondition',
    label: 'Motor Terminal Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'overloadCondition',
    label: 'Overload Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'powerWiringCondition',
    label: 'Power Wiring Condition',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'voltage',
    label: 'Voltage',
    type: 'number',
    unit: 'V',
    section: 'electrical'
  },
  {
    key: 'voltageL1L2',
    label: 'Voltage L1-L2',
    type: 'number',
    unit: 'V',
    section: 'electrical'
  },
  {
    key: 'voltageL1L3',
    label: 'Voltage L1-L3',
    type: 'number',
    unit: 'V',
    section: 'electrical'
  },
  {
    key: 'voltageL2L3',
    label: 'Voltage L2-L3',
    type: 'number',
    unit: 'V',
    section: 'electrical'
  }
];

const SEQUENCE_CONTROLS_CHECKS: ChecklistFieldConfig[] = [
  {
    key: 'dptDifferentialPressureTransmitter',
    label: 'DPT - Differential Pressure Transmitter',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'erraticOperationMalfunctioning',
    label: 'Erratic Operation / Malfunctioning',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'indicationsOnOffTrip',
    label: 'Indications [ON] [OFF] & [TRIP]',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'mccOffOverrideFunction',
    label: 'MCC Off - Override Function',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'msfdDamperFunctional',
    label: 'MSFD - Damper Functional',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'offWithDuctDetectorActivation',
    label: 'Off with Duct Detector activation',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'overrideFscsPanelStatus',
    label: 'Override (FSCS) Panel Status',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'sameTagNameInMccFan',
    label: 'Same Tag Name in MCC & Fan',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'selectorRunStopAuto',
    label: 'Selector [RUN] [STOP] [AUTO]',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'vfdVariableFrequencyDrive',
    label: 'VFD - Variable Frequency Drive',
    type: 'status',
    section: 'sequence'
  }
];

const ALL_CHECKS = [
  ...MECHANICAL_CHECKS,
  ...ELECTRICAL_CHECKS,
  ...SEQUENCE_CONTROLS_CHECKS
];

// Get display value for any field value, handling empty states consistently
function getDisplayValue(value: any, includeEmpty: boolean = true): string {
  if (value === undefined || value === null || String(value).trim() === '') {
    return includeEmpty ? 'N/A' : '';
  }
  
  if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
    return value;
  }
  
  return String(value);
}

// Get sorted fields for a specific section
function getSortedFields(section: 'mechanical' | 'electrical' | 'sequence'): ChecklistFieldConfig[] {
  return ALL_CHECKS
    .filter(field => field.section === section)
    .sort((a, b) => {
      const indexA = ALL_CHECKS.findIndex(check => check.key === a.key);
      const indexB = ALL_CHECKS.findIndex(check => check.key === b.key);
      return indexA - indexB;
    });
}

// Generate filename for Excel export
function generateFileName(checklist: ChecklistData): string {
  try {
    const date = new Date(checklist.generalInfo.date);
    const formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD format
    
    // Clean and format the tag number
    const tagNo = checklist.generalInfo.tagNo
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      .toUpperCase();
    
    // Clean and format equipment name
    const equipmentName = checklist.generalInfo.equipmentName
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/\s+/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      .toUpperCase();
    
    return `${equipmentName}_${tagNo}_${formattedDate}.xlsx`;
  } catch {
    // Fallback to timestamp if date parsing fails
    const timestamp = new Date().toISOString().split('T')[0];
    const fallbackTag = (checklist.generalInfo.tagNo || 'UNKNOWN').replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
    const fallbackEquipment = (checklist.generalInfo.equipmentName || 'CHECKLIST').replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
    return `${fallbackEquipment}_${fallbackTag}_${timestamp}.xlsx`;
  }
}

/**
 * Generate Excel export for a single checklist
 */
export function generateChecklistExcel(checklist: ChecklistData): Uint8Array {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();
    
    // Sheet 1: General Information
    const generalInfoData = [
      ['General Information', ''],
      ['Client Name', checklist.generalInfo.clientName],
      ['Building', checklist.generalInfo.building],
      ['Equipment Name', checklist.generalInfo.equipmentName],
      ['Location', checklist.generalInfo.location],
      ['Tag Number', checklist.generalInfo.tagNo],
      ['Inspection Date', checklist.generalInfo.date],
      ['PPM Attempt', checklist.generalInfo.ppmAttempt],
      ['Inspected By', checklist.generalInfo.inspectedBy],
      ['Approved By', checklist.generalInfo.approvedBy],
      [''],
      ['Remarks', ''],
      ['Notes', checklist.remarks || 'No remarks provided'],
      [''],
      ['Images', ''],
      ['Before Image', checklist.beforeImage ? 'Attached' : 'Not provided'],
      ['After Image', checklist.afterImage ? 'Attached' : 'Not provided']
    ];
    
    const generalInfoSheet = XLSX.utils.aoa_to_sheet(generalInfoData);
    generalInfoSheet['!cols'] = [
      { width: 20 },
      { width: 40 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, generalInfoSheet, 'General Info');
    
    // Sheet 2: Mechanical Checks
    const mechanicalData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    const mechanicalFields = getSortedFields('mechanical');
    
    mechanicalFields.forEach(field => {
      const value = checklist.mechanicalChecks[field.key as keyof typeof checklist.mechanicalChecks];
      const displayValue = getDisplayValue(value, true);
      
      mechanicalData.push([
        field.label,
        displayValue,
        field.unit || '',
        field.type === 'number' ? 'Measurement' : 'Status'
      ]);
    });
    
    const mechanicalSheet = XLSX.utils.aoa_to_sheet(mechanicalData);
    mechanicalSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, mechanicalSheet, 'Mechanical List');
    
    // Sheet 3: Electrical Checks
    const electricalData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    const electricalFields = getSortedFields('electrical');
    
    electricalFields.forEach(field => {
      const value = checklist.electricalChecks[field.key as keyof typeof checklist.electricalChecks];
      const displayValue = getDisplayValue(value, true);
      
      electricalData.push([
        field.label,
        displayValue,
        field.unit || '',
        field.type === 'number' ? 'Measurement' : 'Status'
      ]);
    });
    
    const electricalSheet = XLSX.utils.aoa_to_sheet(electricalData);
    electricalSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, electricalSheet, 'Electrical List');
    
    // Sheet 4: Sequence/Controls Checks
    const sequenceData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    const sequenceFields = getSortedFields('sequence');
    
    sequenceFields.forEach(field => {
      const value = checklist.sequenceControlsChecks[field.key as keyof typeof checklist.sequenceControlsChecks];
      const displayValue = getDisplayValue(value, true);
      
      sequenceData.push([
        field.label,
        displayValue,
        field.unit || '',
        field.type === 'number' ? 'Measurement' : 'Status'
      ]);
    });
    
    const sequenceSheet = XLSX.utils.aoa_to_sheet(sequenceData);
    sequenceSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, sequenceSheet, 'Sequence-Controls List');
    
    // Sheet 5: Summary
    const allConfiguredFields = [
      ...mechanicalFields,
      ...electricalFields,
      ...sequenceFields
    ];
    
    const stats = {
      OK: 0,
      Faulty: 0,
      'N/A': 0,
      Missing: 0,
      total: 0
    };
    
    // Count all configured fields
    allConfiguredFields.forEach(field => {
      const sectionData = field.section === 'mechanical' ? checklist.mechanicalChecks :
                         field.section === 'electrical' ? checklist.electricalChecks :
                         checklist.sequenceControlsChecks;
      
      const value = sectionData[field.key as keyof typeof sectionData];
      const displayValue = getDisplayValue(value, true);
      
      if (displayValue === 'OK' || displayValue === 'Faulty' || displayValue === 'N/A' || displayValue === 'Missing') {
        stats[displayValue as keyof typeof stats]++;
        stats.total++;
      } else if (displayValue !== '') {
        stats.total++;
      }
    });
    
    const okPercentage = stats.total > 0 ? Math.round((stats.OK / stats.total) * 100) : 0;
    const completionPercentage = stats.total > 0 ? Math.round(((stats.total - stats.Missing) / stats.total) * 100) : 0;
    
    const summaryData = [
      ['Inspection Summary', ''],
      [''],
      ['Status Breakdown', ''],
      ['Passed (OK)', stats.OK],
      ['Failed (Faulty)', stats.Faulty],
      ['Not Applicable (N/A)', stats['N/A']],
      ['Missing', stats.Missing],
      ['Total Tests', stats.total],
      [''],
      ['Performance Metrics', ''],
      ['Success Rate (%)', okPercentage],
      ['Completion Rate (%)', completionPercentage],
      ['Critical Issues', stats.Faulty],
      ['Assessment', okPercentage >= 90 ? 'EXCELLENT' : okPercentage >= 70 ? 'SATISFACTORY' : 'NEEDS ATTENTION'],
      [''],
      ['Report Generated', new Date().toLocaleString()]
    ];
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    summarySheet['!cols'] = [
      { width: 25 },
      { width: 20 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Generate Excel file as buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    return new Uint8Array(excelBuffer);
    
  } catch (error) {
    throw new Error(`Failed to generate Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate bulk Excel export with multiple checklists
 */
export function generateBulkExcel(checklists: ChecklistData[]): Uint8Array {
  try {
    const workbook = XLSX.utils.book_new();
    
    // Sheet 1: Summary of all checklists
    const summaryData = [
      ['Bulk Export Summary', ''],
      ['Total Checklists', checklists.length],
      ['Export Date', new Date().toLocaleString()],
      [''],
      ['Checklist Details', '', '', '', '', ''],
      ['#', 'Client Name', 'Equipment Name', 'Tag Number', 'Date', 'Status']
    ];
    
    let totalStats = {
      OK: 0,
      Faulty: 0,
      'N/A': 0,
      Missing: 0,
      total: 0
    };
    
    checklists.forEach((checklist, index) => {
      // Calculate stats for this checklist
      const allConfiguredFields = [
        ...getSortedFields('mechanical'),
        ...getSortedFields('electrical'),
        ...getSortedFields('sequence')
      ];
      
      let checklistStats = { OK: 0, Faulty: 0, 'N/A': 0, Missing: 0, total: 0 };
      
      allConfiguredFields.forEach(field => {
        const sectionData = field.section === 'mechanical' ? checklist.mechanicalChecks :
                           field.section === 'electrical' ? checklist.electricalChecks :
                           checklist.sequenceControlsChecks;
        
        const value = sectionData[field.key as keyof typeof sectionData];
        const displayValue = getDisplayValue(value, true);
        
        if (displayValue === 'OK' || displayValue === 'Faulty' || displayValue === 'N/A' || displayValue === 'Missing') {
          checklistStats[displayValue as keyof typeof checklistStats]++;
          checklistStats.total++;
          totalStats[displayValue as keyof typeof totalStats]++;
          totalStats.total++;
        } else if (displayValue !== '') {
          checklistStats.total++;
          totalStats.total++;
        }
      });
      
      const okPercentage = checklistStats.total > 0 ? Math.round((checklistStats.OK / checklistStats.total) * 100) : 0;
      const status = okPercentage >= 90 ? 'EXCELLENT' : okPercentage >= 70 ? 'SATISFACTORY' : 'NEEDS ATTENTION';
      
      summaryData.push([
        index + 1,
        checklist.generalInfo.clientName,
        checklist.generalInfo.equipmentName,
        checklist.generalInfo.tagNo,
        checklist.generalInfo.date,
        status
      ]);
    });
    
    // Add overall statistics
    const overallOkPercentage = totalStats.total > 0 ? Math.round((totalStats.OK / totalStats.total) * 100) : 0;
    summaryData.push(
      [''],
      ['Overall Statistics', ''],
      ['Total Tests', totalStats.total],
      ['Passed (OK)', totalStats.OK],
      ['Failed (Faulty)', totalStats.Faulty],
      ['Not Applicable (N/A)', totalStats['N/A']],
      ['Missing', totalStats.Missing],
      ['Overall Success Rate (%)', overallOkPercentage],
      ['Overall Assessment', overallOkPercentage >= 90 ? 'EXCELLENT' : overallOkPercentage >= 70 ? 'SATISFACTORY' : 'NEEDS ATTENTION']
    );
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    summarySheet['!cols'] = [
      { width: 5 },
      { width: 20 },
      { width: 25 },
      { width: 15 },
      { width: 12 },
      { width: 15 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Add individual checklist sheets (limit to first 10 to avoid excessive file size)
    const maxSheets = Math.min(checklists.length, 10);
    
    for (let i = 0; i < maxSheets; i++) {
      const checklist = checklists[i];
      const sheetName = `${checklist.generalInfo.tagNo || `Checklist${i + 1}`}`.substring(0, 31); // Excel sheet name limit
      
      // Create individual checklist data
      const checklistData = [
        ['General Information', ''],
        ['Client Name', checklist.generalInfo.clientName],
        ['Building', checklist.generalInfo.building],
        ['Equipment Name', checklist.generalInfo.equipmentName],
        ['Location', checklist.generalInfo.location],
        ['Tag Number', checklist.generalInfo.tagNo],
        ['Inspection Date', checklist.generalInfo.date],
        ['PPM Attempt', checklist.generalInfo.ppmAttempt],
        ['Inspected By', checklist.generalInfo.inspectedBy],
        ['Approved By', checklist.generalInfo.approvedBy],
        [''],
        ['All Checks', '', ''],
        ['Field', 'Value/Status', 'Section']
      ];
      
      // Add all fields from all sections
      const allFields = [
        ...getSortedFields('mechanical'),
        ...getSortedFields('electrical'),
        ...getSortedFields('sequence')
      ];
      
      allFields.forEach(field => {
        const sectionData = field.section === 'mechanical' ? checklist.mechanicalChecks :
                           field.section === 'electrical' ? checklist.electricalChecks :
                           checklist.sequenceControlsChecks;
        
        const value = sectionData[field.key as keyof typeof sectionData];
        const displayValue = getDisplayValue(value, true);
        
        checklistData.push([
          field.label,
          displayValue,
          field.section.charAt(0).toUpperCase() + field.section.slice(1)
        ]);
      });
      
      const checklistSheet = XLSX.utils.aoa_to_sheet(checklistData);
      checklistSheet['!cols'] = [
        { width: 35 },
        { width: 15 },
        { width: 12 }
      ];
      
      XLSX.utils.book_append_sheet(workbook, checklistSheet, sheetName);
    }
    
    // Generate Excel file as buffer
    const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    return new Uint8Array(excelBuffer);
    
  } catch (error) {
    throw new Error(`Failed to generate bulk Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
} 